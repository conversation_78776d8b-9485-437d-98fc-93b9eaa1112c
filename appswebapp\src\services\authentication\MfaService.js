import BaseService from '@/services/BaseService'
import { getApplicationName, getDeviceId, getDeviceModel } from '@/shared/auth/deviceInfo'

const methodMap = {
  email: 1, // AuthenticationEnums.MfaMethodEnum.Email
  sms: 2 // AuthenticationEnums.MfaMethodEnum.Sms
}

class MfaService extends BaseService {
  startSetup (methodName) {
    const key = (methodName || '').toString().toLowerCase()
    const methodVal = methodMap[key]
    if (!methodVal) {
      return Promise.reject(new Error('Unsupported MFA method'))
    }
    return this.axios.post('/api/auth/mfa/setup_start', {
      Method: methodVal,
      ApplicationName: getApplicationName(),
      DeviceId: getDeviceId(),
      DeviceModel: getDeviceModel()
    })
  }

  finishSetup (challengeId, code) {
    return this.axios.post('/api/auth/mfa/setup_finish', {
      ChallengeId: challengeId,
      Code: code
    })
  }

  getAccountStatus () {
    return this.axios.get('/api/auth/mfa/account_status')
  }
}

export default new MfaService()
