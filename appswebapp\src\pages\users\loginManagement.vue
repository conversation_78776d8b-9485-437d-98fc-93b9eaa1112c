<template>
  <div>
    <h4>Login Management</h4>
    <div v-if="!isLoading && userInfo">
      <b-card>
        <edit-settings-helper
          :isLoading="isSaving"
          :isViewMode="isViewMode"
          title="User Credentials"
          @save="onSave"
          @cancel="onCancel"
          @changeMode="onChangeMode"
        >
          <div slot="settings-content">
            <ValidationObserver ref="validator">
              <!-- Login / Service Name -->
              <ValidationProvider v-if="userInfo.userType === userTypes.ebizServiceUser.value" name="Service Name" rules="required" v-slot="{ errors }">
                <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
                  <span slot="title">Service Name:</span>
                  <b-form-input name="ServiceName" slot="payload" v-model="userInfoCopy.userName" :disabled="isViewMode" />
                </detail-row>
              </ValidationProvider>
              <ValidationProvider v-else name="Login" rules="required" v-slot="{ errors }">
                <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
                  <span slot="title">Login:</span>
                  <b-form-input name="Login" slot="payload" v-model="userInfoCopy.userName" :disabled="isViewMode" />
                </detail-row>
              </ValidationProvider>

              <!-- AccountId (readonly) for CP user -->
              <detail-row v-if="userInfo.userType === userTypes.cpUser.value" :fixed-payload-width="true">
                <span slot="title">AccountId:</span>
                <span slot="payload" class="text-muted">{{ userInfoCopy.accountId }}</span>
              </detail-row>

              <!-- Service Description or First/Last Name -->
              <ValidationProvider v-if="userInfo.userType === userTypes.ebizServiceUser.value" name="Service Description" rules="required" v-slot="{ errors }">
                <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
                  <span slot="title">Service Description:</span>
                  <b-form-input name="ServiceDescription" slot="payload" v-model="userInfoCopy.firstName" :disabled="isViewMode" />
                </detail-row>
              </ValidationProvider>

              <template v-else>
                <!-- First Name (readonly like in edit mode of modal) -->
                <detail-row :title-position="'start'" :fixed-payload-width="true">
                  <span slot="title">First Name:</span>
                  <span slot="payload" class="text-muted">{{ userInfoCopy.firstName }}</span>
                </detail-row>
                <!-- Last Name (readonly like in edit mode of modal) -->
                <detail-row :title-position="'start'" :fixed-payload-width="true">
                  <span slot="title">Last Name:</span>
                  <span slot="payload" class="text-muted">{{ userInfoCopy.lastName }}</span>
                </detail-row>
              </template>

              <!-- Password / Repeat Password -->
              <ValidationProvider rules="password:@confirm" v-slot="{ errors }">
                <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
                  <span slot="title">Password:</span>
                  <b-form-input type="password" slot="payload" v-model="userInfoCopy.password" :disabled="isViewMode" :placeholder="'Leave empty if same'" autocomplete="new-password" />
                </detail-row>
              </ValidationProvider>
              <ValidationProvider name="confirm" vid="confirm" v-slot="{ errors }">
                <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
                  <span slot="title">Repeat Password:</span>
                  <b-form-input name="confirm" type="password" slot="payload" v-model="userInfoCopy.rePassword" :disabled="isViewMode" :placeholder="'Leave empty if same'" autocomplete="new-password" />
                </detail-row>
              </ValidationProvider>

              <!-- Email: input only when editing service user, else readonly -->
              <ValidationProvider v-if="showEmailInput" name="Email" rules="required|email" v-slot="{ errors }">
                <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors[0]">
                  <span slot="title">Email:</span>
                  <div slot="payload" class="d-flex align-items-center">
                    <b-form-input name="Email" type="email" v-model="userInfoCopy.email" :disabled="isViewMode" />
                    <span v-if="isViewMode && userInfo && userInfo.userType === userTypes.cpUser.value && userInfoCopy.isEmailVerified" class="ml-2" :class="userInfoCopy.isEmailVerified ? 'text-success' : 'text-danger'">
                      {{ userInfoCopy.isEmailVerified ? 'Verified' : 'Not verified' }}
                    </span>
                    <b-button
                      v-if="isViewMode && userInfo && userInfo.userType === userTypes.cpUser.value && !userInfoCopy.isEmailVerified"
                      size="sm"
                      class="ml-2"
                      variant="danger"
                      @click="startMfaVerification('email')"
                    >Verify</b-button>
                  </div>
                </detail-row>
              </ValidationProvider>
              <detail-row v-else :title-position="'start'" :fixed-payload-width="true">
                <span slot="title">Email:</span>
                <div slot="payload" class="d-flex align-items-center">
                  <span class="text-muted">{{ userInfoCopy.email }}</span>
                  <span v-if="isViewMode && userInfo && userInfo.userType === userTypes.cpUser.value && userInfoCopy.isEmailVerified" class="ml-2" :class="userInfoCopy.isEmailVerified ? 'text-success' : 'text-danger'">
                    {{ userInfoCopy.isEmailVerified ? 'Verified' : 'Not verified' }}
                  </span>
                  <b-button
                    v-if="isViewMode && userInfo && userInfo.userType === userTypes.cpUser.value && !userInfoCopy.isEmailVerified"
                    size="sm"
                    class="ml-2"
                    variant="danger"
                    @click="startMfaVerification('email')"
                  >Verify</b-button>
                </div>
              </detail-row>

              <!-- MFA Phone Number -->
              <detail-row v-if="userInfo && userInfo.userType === userTypes.cpUser.value" :title-position="'start'" :fixed-payload-width="true">
                <span slot="title">MFA Phone:</span>
                <div slot="payload" class="d-flex align-items-center">
                  <phone-input v-model="userInfoCopy.mfaPhoneNumber" :disabled="isViewMode" placeholder="(*************" />
                  <span v-if="isViewMode && userInfoCopy && userInfoCopy.mfaPhoneNumber && userInfoCopy.isMfaPhoneNumberVerified" class="ml-2" :class="userInfoCopy.isMfaPhoneNumberVerified ? 'text-success' : 'text-danger'">
                    {{ userInfoCopy.isMfaPhoneNumberVerified ? 'Verified' : 'Not verified' }}
                  </span>
                  <b-button
                    v-if="isViewMode && userInfoCopy && userInfoCopy.mfaPhoneNumber && !userInfoCopy.isMfaPhoneNumberVerified"
                    size="sm"
                    class="ml-2"
                    variant="danger"
                    @click="startMfaVerification('sms')"
                  >Verify</b-button>
                </div>
              </detail-row>

              <!-- Roles (admin only) -->
              <detail-row v-if="hasFullAccess && userInfo.userType !== userTypes.ebizServiceUser.value" :fixed-payload-width="true">
                <span slot="title">Roles:</span>
                <multiselect
                  slot="payload"
                  :options="rolesOptions"
                  v-model="selectedUsersRoles"
                  :multiple="true"
                  track-by="value"
                  label="text"
                  :searchable="false"
                  :disabled="isViewMode"
                />
              </detail-row>

              <!-- Inactive (admin only, readonly as in modal) -->
              <detail-row v-if="hasFullAccess" :fixed-payload-width="true">
                <span slot="title">Inactive:</span>
                <b-form-checkbox slot="payload" v-model="userInfoCopy.isInactive" disabled></b-form-checkbox>
              </detail-row>

              <!-- Default App (for non-service user) -->
              <detail-row v-if="userInfo.userType !== userTypes.ebizServiceUser.value" :fixed-payload-width="true">
                <span slot="title">Default App:</span>
                <b-form-select slot="payload" v-model="userInfoCopy.defaultApp" :options="getAppOptions" :disabled="isViewMode"></b-form-select>
              </detail-row>
            </ValidationObserver>
          </div>
        </edit-settings-helper>
      </b-card>
    </div>
    <div v-else class="my-5 text-center">
      <b-spinner variant="primary"></b-spinner>
      <p class="mt-2">Loading user information...</p>
    </div>

    <!-- MFA Verification Modal -->
    <b-modal
      id="mfa-setup-modal"
      :visible="isMfaModalVisible"
      @hide="closeMfaModal"
      title="Verify Contact"
      @ok="finishMfaVerification"
      @cancel="closeMfaModal"
      :ok-disabled="!codeInput || isMfaFinishing"
      ok-title="Verify Code"
      cancel-title="Cancel"
      size="sm"
    >
      <div class="text-muted mb-2">
        Enter the verification code sent to your {{ currentMfaMethod === 'email' ? 'email' : 'mobile phone' }}.
      </div>
      <b-form-input v-model="codeInput" placeholder="Enter code" autocomplete="one-time-code" @keyup.enter="finishMfaVerification" />

      <template #modal-ok>
        <b-spinner small v-if="isMfaFinishing" class="mr-1" />
        Verify Code
      </template>
    </b-modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import permissions from '@/shared/common/permissions'
import { userTypes, appTypes } from '@/shared/users/constants'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import Multiselect from 'vue-multiselect'
import globals from '@/globals'
import UserManagementService from '@/services/users/UserManagementService'
import { ValidationObserver, ValidationProvider } from 'vee-validate'
import phoneInput from '@/components/_shared/phoneInput'
import MfaService from '@/services/authentication/MfaService'

export default {
  name: 'users-login-management',
  metaInfo: {
    title: 'Login Management'
  },
  data () {
    return {
      isLoading: true,
      isSaving: false,
      isViewMode: true,
      userTypes,
      userInfo: null,
      userInfoCopy: {},
      roles: [],
      selectedUsersRoles: [],
      // MFA setup state
      isMfaModalVisible: false,
      mfaChallengeId: null,
      currentMfaMethod: null, // 'email' | 'sms'
      codeInput: '',
      isMfaFinishing: false
    }
  },
  computed: {
    ...mapGetters('users', { userInfoStore: 'userInfo' }),
    user () {
      return (this.userInfoStore || {}).user || {}
    },
    hasFullAccess () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.FullAccess)
    },
    rolesOptions () {
      const options = []
      Object.values(this.roles).forEach(x => options.push({ value: x.id, text: x.name }))
      return options
    },
    getAppOptions () {
      return Object.values(appTypes)
    },
    showEmailInput () {
      return this.userInfo && this.userInfo.userType === userTypes.ebizServiceUser.value
    }
  },
  components: {
    'detail-row': detailRow,
    'edit-settings-helper': editSettingsHelper,
    'multiselect': Multiselect,
    'phone-input': phoneInput,
    ValidationObserver,
    ValidationProvider
  },
  created () {
    this.loadUser()
  },
  methods: {
    async loadUser () {
      try {
        const id = this.$store.state.users.informationForUser.user.userId
        const response = await UserManagementService.getUser(id)
        this.userInfo = response.data
        this.userInfoCopy = globals().getClonedValue(this.userInfo)
        if (this.hasFullAccess && this.userInfo.userType !== userTypes.ebizServiceUser.value) {
          const rolesResponse = await UserManagementService.getUserRoles()
          this.roles = rolesResponse.data || []
          this.selectedUsersRoles = []
          if (this.userInfo.roles && this.userInfo.roles.length > 0) {
            this.userInfo.roles.forEach(x => {
              const r = this.rolesOptions.find(role => role.value === x)
              if (r) this.selectedUsersRoles.push(r)
            })
          }
        }
      } catch (ex) {
        this.$toaster.exception(ex, 'Cannot load user info')
        this.$logger.handleError(ex, 'Cannot load user info')
      } finally {
        this.isLoading = false
      }
    },
    onChangeMode (mode) {
      this.isViewMode = mode
    },
    onCancel () {
      this.userInfoCopy = globals().getClonedValue(this.userInfo)
      this.$refs.validator.reset()
      this.isViewMode = true
    },
    async onSave () {
      const isValid = await this.$refs.validator.validate()
      if (!isValid) return
      try {
        this.isSaving = true
        await this.mapUserRoles()
        // sanitize MFA phone number: keep digits only
        if (this.userInfoCopy.mfaPhoneNumber) {
          this.userInfoCopy.mfaPhoneNumber = (this.userInfoCopy.mfaPhoneNumber.match(/\d/g) || []).join('')
        }
        await UserManagementService.updateUser(this.userInfoCopy.id, this.userInfoCopy)
        this.$toaster.success('Updated User Successfully')
        // reload fresh data
        await this.loadUser()
        this.isViewMode = true
      } catch (ex) {
        this.$toaster.exception(ex, 'Something went wrong!')
      } finally {
        this.isSaving = false
      }
    },
    async mapUserRoles () {
      if (!this.hasFullAccess || this.userInfo.userType === userTypes.ebizServiceUser.value) {
        return
      }
      this.userInfoCopy.roles = this.selectedUsersRoles.map(x => x.value)
    },
    async startMfaVerification (method) {
      try {
        const resp = await MfaService.startSetup(method)
        const data = (resp || {}).data || {}
        this.mfaChallengeId = data.challengeId
        this.currentMfaMethod = method
        this.isMfaModalVisible = true
        this.$toaster.success('Verification code sent.')
      } catch (ex) {
        const msg = (((ex || {}).response || {}).data || '')
        this.$logger.handleError(ex, 'Failed to start MFA verification')
        this.$toaster.error(msg && msg.length < 160 ? msg : 'Failed to start verification')
      }
    },
    async finishMfaVerification () {
      if (!this.codeInput || !this.mfaChallengeId) return
      this.isMfaFinishing = true
      try {
        await MfaService.finishSetup(this.mfaChallengeId, this.codeInput)
        this.$toaster.success('Verification successful')
        this.closeMfaModal()
        await this.loadUser()
      } catch (ex) {
        const msg = (((ex || {}).response || {}).data || '')
        this.$toaster.error(msg && msg.length < 160 ? msg : 'Verification failed')
        this.closeMfaModal()
      } finally {
        this.isMfaFinishing = false
      }
    },
    closeMfaModal () {
      this.isMfaModalVisible = false
      this.mfaChallengeId = null
      this.currentMfaMethod = null
      this.codeInput = ''
    }
  }
}
</script>
