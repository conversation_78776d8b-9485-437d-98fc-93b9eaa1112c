import { shallowMount, createLocalVue } from '@vue/test-utils'
import Vuex from 'vuex'
import loginManagement from '@/pages/users/loginManagement'
import { userTypes } from '@/shared/users/constants'

const localVue = createLocalVue()
localVue.use(Vuex)

describe('loginManagement.vue', () => {
  let wrapper
  let store
  let actions
  let getters

  beforeEach(() => {
    actions = {}
    
    getters = {
      'users/userInfo': () => ({
        user: {
          hasPermissions: jest.fn().mockReturnValue(true)
        }
      })
    }

    store = new Vuex.Store({
      actions,
      getters,
      state: {
        users: {
          informationForUser: {
            user: {
              userId: 'test-user-id'
            }
          }
        }
      }
    })

    wrapper = shallowMount(loginManagement, {
      store,
      localVue,
      mocks: {
        $toaster: {
          exception: jest.fn(),
          success: jest.fn(),
          error: jest.fn()
        },
        $logger: {
          handleError: jest.fn()
        }
      },
      stubs: {
        MfaService: {
          getAccountStatus: jest.fn().mockResolvedValue({
            data: { isMfaEnabled: true }
          })
        }
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  describe('shouldShowVerifyButton computed property', () => {
    it('should return true when user is CP user and MFA is enabled', () => {
      wrapper.setData({
        userInfo: {
          userType: userTypes.cpUser.value
        },
        accountMfaEnabled: true
      })

      expect(wrapper.vm.shouldShowVerifyButton).toBe(true)
    })

    it('should return false when user is CP user but MFA is disabled', () => {
      wrapper.setData({
        userInfo: {
          userType: userTypes.cpUser.value
        },
        accountMfaEnabled: false
      })

      expect(wrapper.vm.shouldShowVerifyButton).toBe(false)
    })

    it('should return false when user is not CP user even if MFA is enabled', () => {
      wrapper.setData({
        userInfo: {
          userType: userTypes.ebizServiceUser.value
        },
        accountMfaEnabled: true
      })

      expect(wrapper.vm.shouldShowVerifyButton).toBe(false)
    })

    it('should return false when userInfo is null', () => {
      wrapper.setData({
        userInfo: null,
        accountMfaEnabled: true
      })

      expect(wrapper.vm.shouldShowVerifyButton).toBe(false)
    })
  })

  describe('loadAccountMfaSettings method', () => {
    it('should set accountMfaEnabled to true when account has MFA enabled', async () => {
      const mockMfaService = {
        getAccountStatus: jest.fn().mockResolvedValue({
          data: { isMfaEnabled: true }
        })
      }

      // Mock the MfaService import
      wrapper.vm.constructor.__Rewire__('MfaService', mockMfaService)

      wrapper.setData({
        userInfo: {
          accountId: 123,
          userType: userTypes.cpUser.value
        }
      })

      await wrapper.vm.loadAccountMfaSettings()

      expect(mockMfaService.getAccountStatus).toHaveBeenCalled()
      expect(wrapper.vm.accountMfaEnabled).toBe(true)
    })

    it('should set accountMfaEnabled to false when API call fails', async () => {
      const mockMfaService = {
        getAccountStatus: jest.fn().mockRejectedValue(new Error('API Error'))
      }

      // Mock the MfaService import
      wrapper.vm.constructor.__Rewire__('MfaService', mockMfaService)

      wrapper.setData({
        userInfo: {
          accountId: 123,
          userType: userTypes.cpUser.value
        }
      })

      await wrapper.vm.loadAccountMfaSettings()

      expect(wrapper.vm.accountMfaEnabled).toBe(false)
      expect(wrapper.vm.$logger.handleError).toHaveBeenCalled()
    })
  })
})
